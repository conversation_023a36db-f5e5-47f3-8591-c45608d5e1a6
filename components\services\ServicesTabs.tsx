'use client';

import React from 'react';
import { services } from './services';

export default function ServicesTabs() {
  return (
    <section className="px-4 sm:px-6 md:px-20 py-10 lg:ml-30 lg:mr-20">
      <div className="max-w-5xl mx-auto flex flex-col gap-10 md:gap-20">
        {services.map((service, idx) => (
          <div
            key={idx}
            className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-16 items-center min-h-[320px]"
          >
            <div>
              <h3 className="text-xl font-semibold">{service.heading}</h3>
              <p className="text-gray-600 mt-2">{service.desc}</p>
              <ul className="mt-4 list-disc list-inside text-gray-700 space-y-1">
                {service.points.map((point, i) => (
                  <li key={i}>{point}</li>
                ))}
              </ul>
            </div>
            <div className="w-full h-56 md:h-64 flex items-center justify-center">
              <img
                src={service.image}
                alt={service.heading}
                className="w-full h-full object-cover rounded-xl shadow-md"
              />
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
