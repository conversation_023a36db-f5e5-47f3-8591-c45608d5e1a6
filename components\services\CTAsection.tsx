'use client';

import React from 'react';
import { ArrowRight } from 'lucide-react'; // Optional: Install lucide-react
import Link from 'next/link';

const CTASection = () => {
  return (
    <section className="bg-sky-500 py-20 px-4">
      <div className="text-center text-white max-w-2xl mx-auto">
        <h2 className="text-2xl sm:text-3xl font-semibold mb-4">
          Ready to Implement Your Solution?
        </h2>
        <p className="text-sm sm:text-base mb-8">
          Let's discuss your requirements and how our services can help you
          achieve your business goals.
        </p>
        <Link
          href="/contact"
          className="inline-flex items-center gap-2 bg-white text-sky-600 font-medium text-sm px-5 py-3 rounded-full shadow-sm hover:bg-gray-100 transition"
        >
          Contact Our Team <ArrowRight className="w-4 h-4" />
        </Link>
      </div>
    </section>
  );
};

export default CTASection;
