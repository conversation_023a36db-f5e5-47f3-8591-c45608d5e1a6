import * as React from "react";
const aboutbg = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={529}
    height={570}
    viewBox="0 0 529 570"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M42.6896 236.458C27.7043 303.659 -13.0565 335.814 7.64566 401.48C31.5228 477.217 124.322 508.593 171.192 514.694C209.349 519.66 286.077 568.644 324.555 568.541C388.605 568.371 410.342 520.099 439.93 463.293C463.066 418.874 487.031 414.869 508.092 369.43C538.297 304.265 533.804 270.761 491.89 212.434C456.365 162.999 439.55 62.9501 382.383 42.0275C313.683 16.884 264.75 -9.83425 193.354 6.11779C137.959 18.4945 99.3287 42.3545 76.3931 94.2744C55.2478 142.142 54.0788 185.383 42.6896 236.458Z"
      fill="#05A0E2"
      stroke="#04E6FC"
      strokeWidth={2}
      strokeLinejoin="round"
      strokeDasharray="2 7"
    />
  </svg>
);
export default aboutbg;
