@import "tailwindcss";


/* ===============================
   🌗 Custom Color and Font Variables
   =============================== */

:root {
  --color-background: #ffffff;
  --color-foreground: #171717;

  --font-mono: var(--font-inter);
}

/* ===============================
   🌒 Dark Mode Variables
   =============================== */

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: #0a0a0a;
    --color-foreground: #ededed;
  }
}

/* ===============================
   🧱 Global Base Styles
   =============================== */

html {
  scroll-behavior: smooth;
  /* Ensure consistent text rendering across platforms */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
  /* Improve font rendering on different platforms */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

* {
  font-family: var(--font-inter);
  /* Ensure consistent box-sizing */
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-inter) !important;
  transition: background-color 0.3s, color 0.3s;
  /* Prevent horizontal scrolling issues */
  overflow-x: hidden;
  /* Ensure consistent line height */
  line-height: 1.5;
  /* Improve text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure Inter font is applied to all form elements and special cases */
input, textarea, select, button,
h1, h2, h3, h4, h5, h6,
p, span, div, a, li, ul, ol,
table, th, td, label {
  font-family: var(--font-inter) !important;
}

/* ===============================
   🎭 Smooth Animations
   =============================== */

*, *::before, *::after {
  font-family: var(--font-inter) !important;
  transition: all 0.3s ease-in-out;
  scroll-behavior: smooth;
}

/* Smooth transitions for common interactive elements */
button, a, .cursor-pointer {
  transition: all 0.2s ease-in-out;
}

.text-justify {
  text-align: justify;
}

/* Custom blob shape */
.clip-blob {
  clip-path: path(
    "M183.5,-162.2C222.4,-112.3,229.9,-36.9,212.7,33.4C195.4,103.8,153.4,168.9,93.6,197.6C33.8,226.2,-43.7,218.4,-109.6,185.3C-175.6,152.1,-229.9,93.5,-234.7,33.2C-239.5,-27,-194.9,-89,-139,-135.4C-83.1,-181.7,-16,-211.4,54.4,-214.8C124.8,-218.3,166.7,-195.1,183.5,-162.2Z"
  );
}

/* ===============================
   🖥️ Cross-Platform Consistency
   =============================== */

/* High DPI display adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body {
    font-weight: 400;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
  }
}

/* Windows-specific adjustments */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  body {
    font-family: var(--font-inter), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  }
}

/* macOS-specific adjustments */
@supports (-webkit-backdrop-filter: blur(1px)) {
  body {
    font-family: var(--font-inter), -apple-system, BlinkMacSystemFont, sans-serif !important;
  }
}

/* Ensure consistent scaling across different zoom levels */
@media (min-resolution: 144dpi) and (max-resolution: 191dpi) {
  html {
    font-size: 16px;
  }
}

@media (min-resolution: 192dpi) {
  html {
    font-size: 16px;
  }
}

/* Fix for Windows Chrome scaling issues */
@media screen and (-webkit-min-device-pixel-ratio: 1.25) {
  html {
    zoom: 0.8;
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 1.5) {
  html {
    zoom: 0.67;
  }
}

/* Consistent button and input styling across platforms */
button, input, select, textarea {
  font-family: var(--font-inter) !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Ensure images scale consistently */
img {
  max-width: 100%;
  height: auto;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* ===============================
   🎯 Figma Design Specific Styles
   =============================== */

/* Grid pattern for hero section */
.hero-grid {
  background-image:
    linear-gradient(rgba(255,255,255,0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.08) 1px, transparent 1px);
  background-size: 40px 40px;
}

/* Ensure consistent text rendering for headings */
h1, h2, h3, h4, h5, h6 {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Navigation dots styling */
.nav-dot {
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-dot:hover {
  transform: scale(1.2);
}

/* Ensure consistent button styling */
.btn-gradient {
  background: linear-gradient(to bottom, #04E6FC, #0664CC);
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(to bottom, #04c9de, #0559b4);
  transform: translateY(-1px);
}
