'use client';

import Image from 'next/image';
import Link from 'next/link';
import TypewriterEffect from './TypewriterEffect';

export default function Hero() {
  return (
    <section className="relative h-screen w-full overflow-hidden">
      {/* Background Video */}
      <video
        src="/video.mp4"
        autoPlay
        muted
        loop
        playsInline
        className="absolute inset-0 h-full w-full object-cover -z-30"
      />

      {/* Dark Overlay */}
      <div className="absolute inset-0 bg-black/80 -z-20" />

      {/* Grid Background Pattern */}
      <div
        className="absolute inset-0 -z-10 opacity-20"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px'
        }}
      />

<header className="absolute top-6 left-6 z-50">
  <div className="flex items-center gap-3">
    <img
      src="/logo-icon.png"
      alt="Logo Icon"
      className="w-8 h-8 shrink-0"
    />
    <div className="leading-none">
      <h1 className="text-white font-semibold text-lg leading-tight mb-0">
        Prolytech
      </h1>
      <p className="text-[#05A0E2] text-[8px] font-bold tracking-wider leading-tight mt-0">
        DEFINING THE CURVE OF WHAT'S NEXT
      </p>
    </div>
  </div>
</header>


      <div className="relative z-10 h-full w-full flex items-center">
        <div className="w-full px-6 md:px-12 lg:px-24">
          <div className="max-w-2xl">
            <h1 className="font-inter text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6" style={{ fontFamily: 'Inter' }}>
              <span className="block text-white mb-2">Powering the Next Wave</span>
              <span className="inline-flex items-center text-white">
                of&nbsp;
                <TypewriterEffect
                  phrases={[
                    'Digital Innovation',
                    'Intelligent Automation',
                    'Scalable Growth',
                    'Cloud Scalability'
                  ]}
                  className="text-[#05A0E2]"
                  typingSpeed={50}
                  deletingSpeed={25}
                  pauseDuration={2500}
                />
              </span>
            </h1>

            <p className="text-gray-300 text-base md:text-lg leading-relaxed mb-8 max-w-xl">
              We architect and deliver high-performance platforms—social, transactional, and intelligent—at startup speed and enterprise scale.
            </p>

            <Link
              href="/contact"
              className="inline-block px-8 py-3 text-white font-medium text-base rounded-full shadow-lg transition-all duration-300
                bg-[linear-gradient(to_bottom,#04E6FC,#0664CC)] hover:bg-[linear-gradient(to_bottom,#04c9de,#0559b4)] hover:shadow-xl"
            >
              Schedule a Consultation
            </Link>
          </div>
        </div>
      </div>

      {/* Navigation Dots */}
      <div className="absolute left-6 top-1/2 transform -translate-y-1/2 z-50 flex flex-col gap-4">
        <div className="w-3 h-3 rounded-full bg-white"></div>
        <div className="w-3 h-3 rounded-full bg-white/30"></div>
        <div className="w-3 h-3 rounded-full bg-white/30"></div>
        <div className="w-3 h-3 rounded-full bg-white/30"></div>
      </div>
    </section>
  );
}
