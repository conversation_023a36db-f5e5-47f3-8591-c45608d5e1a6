import * as React from "react";
const productionicon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={32}
    height={32}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M17.1068 2.90661C16.7594 2.74815 16.382 2.66614 16.0001 2.66614C15.6183 2.66614 15.2409 2.74815 14.8934 2.90661L3.46678 8.10661C3.23018 8.21094 3.02902 8.38181 2.8878 8.59842C2.74658 8.81503 2.67139 9.06803 2.67139 9.32661C2.67139 9.58519 2.74658 9.8382 2.8878 10.0548C3.02902 10.2714 3.23018 10.4423 3.46678 10.5466L14.9068 15.7599C15.2542 15.9184 15.6316 16.0004 16.0134 16.0004C16.3953 16.0004 16.7727 15.9184 17.1201 15.7599L28.5601 10.5599C28.7967 10.4556 28.9979 10.2847 29.1391 10.0681C29.2803 9.85153 29.3555 9.59853 29.3555 9.33995C29.3555 9.08137 29.2803 8.82837 29.1391 8.61176C28.9979 8.39515 28.7967 8.22427 28.5601 8.11995L17.1068 2.90661Z"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.66675 16C2.66612 16.255 2.73864 16.5049 2.87571 16.7199C3.01278 16.935 3.20865 17.1062 3.44009 17.2133L14.9068 22.4267C15.2524 22.5832 15.6274 22.6641 16.0068 22.6641C16.3861 22.6641 16.7612 22.5832 17.1068 22.4267L28.5468 17.2267C28.7828 17.1206 28.9828 16.9481 29.1225 16.7303C29.2622 16.5125 29.3355 16.2587 29.3334 16"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.66675 22.6666C2.66612 22.9216 2.73864 23.1715 2.87571 23.3866C3.01278 23.6016 3.20865 23.7729 3.44009 23.88L14.9068 29.0933C15.2524 29.2498 15.6274 29.3307 16.0068 29.3307C16.3861 29.3307 16.7612 29.2498 17.1068 29.0933L28.5468 23.8933C28.7828 23.7872 28.9828 23.6147 29.1225 23.3969C29.2622 23.1791 29.3355 22.9254 29.3334 22.6666"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default productionicon;
