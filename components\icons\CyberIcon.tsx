import * as React from "react";
const cybericon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={32}
    height={32}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M26.6667 17.3333C26.6667 23.9999 22 27.3333 16.4534 29.2666C16.1629 29.365 15.8474 29.3603 15.56 29.2533C10 27.3333 5.33337 23.9999 5.33337 17.3333V7.99995C5.33337 7.64633 5.47385 7.30719 5.7239 7.05714C5.97395 6.80709 6.31309 6.66662 6.66671 6.66662C9.33337 6.66662 12.6667 5.06662 14.9867 3.03995C15.2692 2.79861 15.6285 2.66602 16 2.66602C16.3716 2.66602 16.7309 2.79861 17.0134 3.03995C19.3467 5.07995 22.6667 6.66662 25.3334 6.66662C25.687 6.66662 26.0261 6.80709 26.2762 7.05714C26.5262 7.30719 26.6667 7.64633 26.6667 7.99995V17.3333Z"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 16L14.6667 18.6667L20 13.3334"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default cybericon;
