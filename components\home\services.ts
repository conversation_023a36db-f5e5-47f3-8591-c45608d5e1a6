
import CustomIcon from "../icons/CustomIcon";
import CloudIcon from "../icons/CloudIcon";
import CyberIcon from "../icons/CyberIcon";
import AIIcon from "../icons/AIIcon";
import ProductIcon from "../icons/ProductIcon";

export interface Service {
  title: string;
  img: string;
  icon: React.ComponentType<any>;
}

export const services: Service[] = [
  {
    title: 'Custom Software Development',
    img: '/assets/images/img1.png',
    icon: CustomIcon,
  },
  {
    title: 'Cloud & DevOps',
    img: '/assets/images/img2.png',
    icon: CloudIcon,
  },
  {
    title: 'Cybersecurity & Compliance',
    img: '/assets/images/img3.png',
    icon: CyberIcon,
  },
  {
    title: 'AI & Automation',
    img: '/assets/images/img4.png',
    icon: AIIcon,
  },
  {
    title: 'Product Engineering',
    img: '/assets/images/img5.png',
    icon: ProductIcon,
  },
];