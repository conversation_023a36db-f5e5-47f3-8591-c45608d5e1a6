'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    company: '',
    message: '',
  });
  const [errors, setErrors] = useState({
    fullName: '',
    message: '',
  });
  const [isNavbarExpanded, setIsNavbarExpanded] = useState(false);

  // Monitor navbar hover state directly
  useEffect(() => {
    const navbar = document.querySelector('nav div[class*="hidden lg:flex"]');

    if (navbar) {
      const handleMouseEnter = () => {
        console.log('Navbar hovered - expanding logo to right');
        setIsNavbarExpanded(true);
      };

      const handleMouseLeave = () => {
        console.log('Navbar unhovered - moving logo back to left');
        setIsNavbarExpanded(false);
      };

      navbar.addEventListener('mouseenter', handleMouseEnter);
      navbar.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        navbar.removeEventListener('mouseenter', handleMouseEnter);
        navbar.removeEventListener('mouseleave', handleMouseLeave);
      };
    }
  }, []);

  const validateName = (name: string) => {
    if (!name) return 'Name is required.';
    if (/\d/.test(name)) return 'Name cannot contain numbers.';
    return '';
  };

  const validateMessage = (message: string) => {
    if (!message) return 'Message is required.';
    if (message.length > 800) return 'Message cannot exceed 800 characters.';
    return '';
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Live validation
    if (name === 'fullName') {
      setErrors((prev) => ({ ...prev, fullName: validateName(value) }));
    }
    if (name === 'message') {
      setErrors((prev) => ({ ...prev, message: validateMessage(value) }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const nameError = validateName(formData.fullName);
    const messageError = validateMessage(formData.message);
    setErrors({ fullName: nameError, message: messageError });
    if (nameError || messageError) return;

    try {
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setFormData({ fullName: '', email: '', company: '', message: '' });
        alert('Message sent successfully!');
      } else {
        alert('Failed to send message.');
      }
    } catch (error) {
      alert('An error occurred. Please try again.');
    }
  };

  console.log('Current navbar expanded state:', isNavbarExpanded);
  return (
    <div className="min-h-screen flex flex-col lg:flex-row items-start justify-between gap-8 px-4 sm:px-6 md:px-10 pt-28 pb-20 bg-white">
      <div className={`fixed top-4 md:left-[250px] ${isNavbarExpanded ? 'lg:left-72' : 'lg:left-24'} z-20 transition-all duration-300 ease-in-out`}>
                <Image
                  src="/logo-F.png"
                  alt="Prolytech"
                  width={300}
                  height={100}
                  className="h-8 sm:h-12 md:h-14 lg:h-10 xl:h-12 w-auto pt-4 lg:pt-4 pl-4"
                />
              </div>
      
      {/* Left Section */}
      <div className="w-full lg:w-1/2 flex flex-col items-start text-left space-y-4 sm:space-y-6 mt-4 sm:mt-8 lg:mt-6 sm:px-2 lg:px-0 max-w-full lg:max-w-none lg:ml-25">
        <span className="text-xs font-semibold tracking-wider uppercase mb-2 block" style={{ color: '#2B7FFF' }}>GET IN TOUCH</span>
        <h1 className="text-base sm:text-lg lg:text-2xl font-semibold text-black mb-2">Launch Your Product With Us</h1>
        <p className="text-gray-600 max-w-xl text-xs sm:text-sm lg:text-base">
          Have a platform idea, mobile app, or AI tool in mind? Let's build it. Reach out to the team at Prolytech for a free consultation.
        </p>
        <div className="text-xs sm:text-sm lg:text-base text-gray-600 flex flex-row space-x-4 sm:space-x-8">
          <span><EMAIL></span>
          <span>+91 8247532770</span>
        </div>
        <div className="relative w-full max-w-xs mt-8">
          <Image
            src="/world.svg"
            alt="World Map"
            width={900}
            height={350}
            className="opacity-80 rounded-md"
          />
          {/* Pin + Line adjusted to point to India */}
          <div
            className="absolute flex flex-col items-center"
            style={{ top: '2%', left: '58%' }}
          >
            <div
              className="bg-white text-gray-800 text-xs font-medium px-3 py-1 rounded-md shadow mb-1"
              style={{ marginBottom: '0.5rem', marginTop: '-3rem' }} // Move label up
            >
              We are here
            </div>
            <div className="w-0.5 h-16 bg-gradient-to-b from-blue-400 to-transparent"></div>
            <div className="w-6 h-6 rounded-full bg-blue-300 opacity-30 blur-[2px]"></div>
          </div>
        </div>
        {/* Our Process Section */}
        <div className="mt-8 bg-white rounded-xl border border-gray-200 shadow-sm p-6 w-full max-w-full sm:max-w-md lg:max-w-lg" style={{boxShadow: '0 2px 8px 0 rgba(16,30,54,0.04)'}}>
          <h2 className="text-lg font-bold mb-5">Our Process</h2>
          <div className="flex flex-col gap-6">
            {/* Step 1 */}
            <div className="flex items-start gap-4">
              <span className="flex items-center justify-center w-9 h-9 bg-blue-50 rounded-lg">
                <svg width="20" height="20" fill="none" stroke="#3B82F6" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="4" y="4" width="12" height="12" rx="4"/><path d="M8 12h.01M12 12h.01M16 12h.01"/></svg>
              </span>
              <div>
                <div className="font-semibold text-black text-base">Initial Consultation</div>
                <div className="text-gray-500 text-sm mt-1">We'll discuss your requirements and project goals in detail.</div>
              </div>
            </div>
            {/* Step 2 */}
            <div className="flex items-start gap-4">
              <span className="flex items-center justify-center w-9 h-9 bg-blue-50 rounded-lg">
                <svg width="20" height="20" fill="none" stroke="#3B82F6" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="5" width="14" height="10" rx="3"/><path d="M16 3v4M8 3v4M3 9h14"/></svg>
              </span>
              <div>
                <div className="font-semibold text-black text-base">Proposal & Scope</div>
                <div className="text-gray-500 text-sm mt-1">We'll define the project scope, timeline, and deliverables.</div>
              </div>
            </div>
            {/* Step 3 */}
            <div className="flex items-start gap-4">
              <span className="flex items-center justify-center w-9 h-9 bg-blue-50 rounded-lg">
                <svg width="20" height="20" fill="none" stroke="#3B82F6" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="10" cy="10" r="8"/><path d="M10 6v4l2 2"/></svg>
              </span>
              <div>
                <div className="font-semibold text-black text-base">Project Kickoff</div>
                <div className="text-gray-500 text-sm mt-1">We'll assemble the right team and start the development process.</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      

      {/* Right Section */}
      <div className="w-full lg:w-1/2 max-w-md mx-auto bg-gradient-to-br from-white to-gray-50 border rounded-xl p-6 sm:p-8 shadow-sm">
        <form onSubmit={handleSubmit} className="space-y-5">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Full name</label>
            <input
              type="text"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              placeholder="Enter your full name"
              className="w-full border px-4 py-2 rounded-md"
              required
            />
            {errors.fullName && (
              <p className="text-red-500 text-xs mt-1">{errors.fullName}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              className="w-full border px-4 py-2 rounded-md"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
            <input
              type="text"
              name="company"
              value={formData.company}
              onChange={handleChange}
              placeholder="Your Company Name"
              className="w-full border px-4 py-2 rounded-md"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Message</label>
            <textarea
              name="message"
              value={formData.message}
              onChange={handleChange}
              rows={4}
              maxLength={800}
              placeholder="Type your message here"
              className="w-full border px-4 py-2 rounded-md"
              required
            />
            <div className="flex justify-between items-center mt-1">
              {errors.message && (
                <p className="text-red-500 text-xs">{errors.message}</p>
              )}
              <span className="text-xs text-gray-400 ml-auto">
                {formData.message.length}/800
              </span>
            </div>
          </div>

          <button
            type="submit"
            className="bg-black text-white px-6 py-2 rounded-md hover:bg-gray-800 w-full sm:w-auto"
          >
            Submit
          </button>
        </form>
      </div>
    </div>
  );
}