import * as React from "react";
const instagramicon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={19}
    height={19}
    viewBox="0 0 19 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M13.25 2H5.75C3.67893 2 2 3.67893 2 5.75V13.25C2 15.3211 3.67893 17 5.75 17H13.25C15.3211 17 17 15.3211 17 13.25V5.75C17 3.67893 15.3211 2 13.25 2Z"
      stroke="#2B7FFF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.5 9.02773C12.5926 9.65191 12.486 10.2894 12.1953 10.8495C11.9047 11.4096 11.4449 11.8638 10.8812 12.1475C10.3176 12.4312 9.67886 12.5299 9.05586 12.4297C8.43287 12.3294 7.85734 12.0353 7.41115 11.5891C6.96496 11.1429 6.67082 10.5674 6.57058 9.94439C6.47033 9.32139 6.56907 8.68265 6.85277 8.11901C7.13647 7.55537 7.59066 7.09553 8.15076 6.80491C8.71086 6.51428 9.34834 6.40767 9.97252 6.50023C10.6092 6.59464 11.1987 6.89132 11.6538 7.34646C12.1089 7.80159 12.4056 8.39103 12.5 9.02773Z"
      stroke="#2B7FFF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.625 5.375H13.6325"
      stroke="#2B7FFF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default instagramicon;
