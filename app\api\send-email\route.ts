import { SESClient, SendEmailCommand } from "@aws-sdk/client-ses";
import { NextResponse } from "next/server";

// Setup SES Client
const ses = new SESClient({
  region: "ap-south-1", // Change to your AWS region
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export async function POST(req: Request) {
  const { fullName, email, message } = await req.json();

  const params = {
    Destination: {
      ToAddresses: ["<EMAIL>"], // Replace with the recipient email
    },
    Message: {
      Subject: {
        Data: "New Contact Form Submission",
      },
      Body: {
        Text: {
          Data: `From: ${fullName}\nEmail: ${email}\n\n${message}`,
        },
      },
    },
    Source: "<EMAIL>", 
  };


  try {
    const command = new SendEmailCommand(params);
    console.log("Sending email with params:", params);
    const response = await ses.send(command);
    console.log("Email sent successfully:", response);
    return NextResponse.json({ success: true, response });
  } catch (error) {
    console.error("SES Error:", error);
    return NextResponse.json({ success: false, error }, { status: 500 });
  }
}
