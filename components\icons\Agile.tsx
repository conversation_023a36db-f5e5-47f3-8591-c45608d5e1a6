import * as React from "react";
const agileicon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={40}
    height={40}
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M26.6663 35V31.6667C26.6663 29.8986 25.964 28.2029 24.7137 26.9526C23.4635 25.7024 21.7678 25 19.9997 25H9.99967C8.23156 25 6.53587 25.7024 5.28563 26.9526C4.03539 28.2029 3.33301 29.8986 3.33301 31.6667V35"
      stroke="#05A0E2"
      strokeWidth={3.33333}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M26.667 5.21338C28.0966 5.584 29.3626 6.41882 30.2665 7.58682C31.1703 8.75481 31.6607 10.1899 31.6607 11.6667C31.6607 13.1436 31.1703 14.5786 30.2665 15.7466C29.3626 16.9146 28.0966 17.7494 26.667 18.12"
      stroke="#05A0E2"
      strokeWidth={3.33333}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M36.667 35V31.6667C36.6659 30.1896 36.1743 28.7546 35.2693 27.5872C34.3643 26.4198 33.0972 25.586 31.667 25.2167"
      stroke="#05A0E2"
      strokeWidth={3.33333}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.9997 18.3333C18.6816 18.3333 21.6663 15.3486 21.6663 11.6667C21.6663 7.98477 18.6816 5 14.9997 5C11.3178 5 8.33301 7.98477 8.33301 11.6667C8.33301 15.3486 11.3178 18.3333 14.9997 18.3333Z"
      stroke="#05A0E2"
      strokeWidth={3.33333}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default agileicon;
