import * as React from "react";
const alicon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={32}
    height={32}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M15.9999 6.66669C16.0015 6.1334 15.8964 5.60518 15.6909 5.11309C15.4853 4.62101 15.1834 4.17499 14.803 3.80129C14.4225 3.42758 13.9712 3.13372 13.4755 2.937C12.9798 2.74028 12.4498 2.64466 11.9166 2.65578C11.3834 2.66689 10.8578 2.78451 10.3708 3.00171C9.88373 3.21892 9.44502 3.53133 9.08047 3.92057C8.71592 4.3098 8.43288 4.76801 8.248 5.26823C8.06313 5.76845 7.98014 6.3006 8.00394 6.83336C7.2202 7.03488 6.49261 7.41209 5.87624 7.93644C5.25988 8.46079 4.77092 9.11852 4.44639 9.85982C4.12187 10.6011 3.97028 11.4066 4.00312 12.2151C4.03596 13.0237 4.25236 13.8142 4.63594 14.5267C3.96151 15.0746 3.43117 15.779 3.09106 16.5786C2.75094 17.3782 2.61135 18.2488 2.68442 19.1147C2.7575 19.9805 3.04103 20.8154 3.51034 21.5467C3.97966 22.278 4.62055 22.8836 5.37727 23.3107C5.28383 24.0337 5.33959 24.7681 5.54112 25.4687C5.74265 26.1693 6.08566 26.8211 6.54898 27.384C7.0123 27.9468 7.58608 28.4087 8.2349 28.741C8.88371 29.0734 9.59377 29.2693 10.3212 29.3165C11.0487 29.3637 11.7781 29.2613 12.4645 29.0155C13.1508 28.7698 13.7794 28.3859 14.3116 27.8877C14.8438 27.3895 15.2682 26.7874 15.5586 26.1188C15.8489 25.4501 15.9992 24.729 15.9999 24V6.66669Z"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16 6.66669C15.9984 6.1334 16.1035 5.60518 16.3091 5.11309C16.5147 4.62101 16.8165 4.17499 17.197 3.80129C17.5774 3.42758 18.0288 3.13372 18.5245 2.937C19.0202 2.74028 19.5502 2.64466 20.0834 2.65578C20.6165 2.66689 21.1421 2.78451 21.6292 3.00171C22.1162 3.21892 22.5549 3.53133 22.9195 3.92057C23.284 4.3098 23.5671 4.76801 23.752 5.26823C23.9368 5.76845 24.0198 6.3006 23.996 6.83336C24.7797 7.03488 25.5073 7.41209 26.1237 7.93644C26.7401 8.46079 27.229 9.11852 27.5536 9.85982C27.8781 10.6011 28.0297 11.4066 27.9968 12.2151C27.964 13.0237 27.7476 13.8142 27.364 14.5267C28.0384 15.0746 28.5688 15.779 28.9089 16.5786C29.249 17.3782 29.3886 18.2488 29.3155 19.1147C29.2425 19.9805 28.9589 20.8154 28.4896 21.5467C28.0203 22.278 27.3794 22.8836 26.6227 23.3107C26.7161 24.0337 26.6604 24.7681 26.4588 25.4687C26.2573 26.1693 25.9143 26.8211 25.451 27.384C24.9877 27.9468 24.4139 28.4087 23.7651 28.741C23.1162 29.0734 22.4062 29.2693 21.6787 29.3165C20.9512 29.3637 20.2218 29.2613 19.5355 29.0155C18.8492 28.7698 18.2205 28.3859 17.6883 27.8877C17.1562 27.3895 16.7318 26.7874 16.4414 26.1188C16.151 25.4501 16.0008 24.729 16 24V6.66669Z"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20 17.3333C18.8806 16.9395 17.9031 16.2227 17.1911 15.2733C16.4791 14.324 16.0646 13.1849 16 12C15.9354 13.1849 15.5209 14.324 14.8089 15.2733C14.0969 16.2227 13.1194 16.9395 12 17.3333"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M23.4653 8.66671C23.788 8.10749 23.9706 7.47846 23.9973 6.83337"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.00391 6.83337C8.03027 7.47835 8.21234 8.10737 8.53457 8.66671"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.63599 14.528C4.87991 14.3293 5.14092 14.1527 5.41599 14"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M26.584 14C26.859 14.1527 27.1201 14.3293 27.364 14.528"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.00011 24C7.08121 24.0004 6.1778 23.7634 5.37744 23.312"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M26.6227 23.312C25.8223 23.7634 24.9189 24.0004 24 24"
      stroke="#04E6FC"
      strokeWidth={2.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default alicon;
