import * as React from "react";
const linkedinicon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width={19}
    height={19}
    viewBox="0 0 19 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M12.5 6.5C13.6935 6.5 14.8381 6.97411 15.682 7.81802C16.5259 8.66193 17 9.80653 17 11V16.25H14V11C14 10.6022 13.842 10.2206 13.5607 9.93934C13.2794 9.65804 12.8978 9.5 12.5 9.5C12.1022 9.5 11.7206 9.65804 11.4393 9.93934C11.158 10.2206 11 10.6022 11 11V16.25H8V11C8 9.80653 8.47411 8.66193 9.31802 7.81802C10.1619 6.97411 11.3065 6.5 12.5 6.5Z"
      stroke="#2B7FFF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5 7.25H2V16.25H5V7.25Z"
      stroke="#2B7FFF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.5 5C4.32843 5 5 4.32843 5 3.5C5 2.67157 4.32843 2 3.5 2C2.67157 2 2 2.67157 2 3.5C2 4.32843 2.67157 5 3.5 5Z"
      stroke="#2B7FFF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default linkedinicon;
