'use client';

import React from 'react';
import { steps } from './steps';


const HowWeDeliver = () => {
	return (
		<section className="bg-gray-50 py-20 px-4 md:px-8 lg:pl-30">
			<div className="text-center max-w-3xl mx-auto mb-16">
				<p className="text-xs font-semibold text-[#05A0E2] uppercase tracking-wider mb-2">
					Our Process
				</p>
				<h2 className="text-3xl font-semibold text-gray-900 mb-2">
					How We Deliver
				</h2>
				<p className="text-gray-500 text-sm sm:text-base">
					Our streamlined approach ensures efficient, transparent, and successful
					project execution.
				</p>
			</div>

			<div className="relative max-w-6xl mx-auto ml-0 mr-0 pl-0 md:ml-0 md:mr-0 md:pl-4 lg:ml-[120px] lg:mr-[120px] lg:pl-4">
				{/* Connecting Line: only between the first and last step circles */}
				<div className="hidden md:block absolute top-8 left-[12.5%] right-[12.5%] h-0.5 bg-blue-200 z-0" />
				<div className="flex flex-col md:flex-row items-center justify-center gap-8 md:gap-0">
					{steps.map((step, index) => (
						<div
							key={index}
							className="flex flex-col items-center text-center max-w-xs w-full mx-auto z-10 md:w-1/4 relative mb-8 md:mb-0"
						>
							{/* Circles: bring to front */}
							<div className="w-12 h-12 flex items-center justify-center rounded-full bg-[#2B7FFF] text-white font-semibold mb-4 mt-1 z-10">
								{step.number}
							</div>
							<h3 className="font-semibold text-gray-900 mb-2">{step.title}</h3>
							<p className="text-gray-600 text-[12px]">{step.description}</p>
						</div>
					))}
				</div>
			</div>
		</section>
	);
};

export default HowWeDeliver;
