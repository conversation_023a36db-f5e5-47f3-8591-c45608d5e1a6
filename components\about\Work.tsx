'use client';

import ScaleIcon from '../icons/Scale';        // from icons/Scale.tsx
import CustomIcon from '../icons/CustomIcon';  // from icons/CustomIcon.tsx
import AgileIcon from '../icons/Agile';        // from icons/Agile.tsx


const OperatingPhilosophy = () => {
  return (
    <section className="w-full bg-white">
      {/* Title */}

      <div className="text-left lg:text-center px-4 py-12 md:pl-24 lg:pl-32">
        <p className="text-sm text-[#4DA1FF] font-semibold uppercase tracking-wide">How We Work</p>
        <h2 className="text-3xl md:text-4xl font-normal mt-2">Our Operating Philosophy</h2>
        <p className="mt-4 text-gray-600 max-w-2xl mx-auto">
          Our approach combines technical excellence with strategic thinking to deliver solutions that drive business growth.
        </p>
      </div>

      
      {/* Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-0 mx-0 md:mx-0">
        {/* Card 1 */}
        <div className="relative bg-black text-white h-[300px] md:h-[350px] lg:h-[400px] w-full">
          <img
            src="/assets/images/img12.png"
            alt="Scale"
            className="absolute inset-0 w-full h-full object-cover object-center opacity-50"
          />
          <div className="relative z-10 flex flex-col items-center justify-center h-full px-4 md:px-6 text-center">
            <div className="bg-[#1F1F1F] p-2 md:p-3 rounded-md mb-3 md:mb-4">
              <ScaleIcon className="w-5 h-5 md:w-6 md:h-6 text-[#4DA1FF]" />
            </div>
            <h3 className="text-base md:text-lg font-semibold mb-2">Design for Scale</h3>
            <p className="text-xs md:text-sm text-gray-300 max-w-xs leading-relaxed">
              We design for performance, flexibility, and scale. Every solution is built to handle growth without compromising on speed or reliability.
            </p>
          </div>
        </div>

        {/* Card 2 */}
        <div className="relative bg-black text-white h-[300px] md:h-[350px] lg:h-[400px] w-full">
          <img
            src="/assets/images/img13.png"
            alt="Custom"
            className="absolute inset-0 w-full h-full object-cover object-center opacity-50"
          />
          <div className="relative z-10 flex flex-col items-center justify-center h-full px-4 md:px-6 text-center">
            <div className="bg-[#1F1F1F] p-2 md:p-3 rounded-md mb-3 md:mb-4">
              <CustomIcon className="w-5 h-5 md:w-6 md:h-6 text-[#4DA1FF]" />
            </div>
            <h3 className="text-base md:text-lg font-semibold mb-2">Future-Ready Technology</h3>
            <p className="text-xs md:text-sm text-gray-300 max-w-xs leading-relaxed">
              We build with future-readiness in mind: AI-ready, API-first, modular architectures that can adapt to changing business needs.
            </p>
          </div>
        </div>

        {/* Card 3 */}
        <div className="relative bg-black text-white h-[300px] md:h-[350px] lg:h-[400px] w-full">
          <img
            src="/assets/images/img14.png"
            alt="Agile"
            className="absolute inset-0 w-full h-full object-cover object-center opacity-50"
          />
          <div className="relative z-10 flex flex-col items-center justify-center h-full px-4 md:px-6 text-center">
            <div className="bg-[#1F1F1F] p-2 md:p-3 rounded-md mb-3 md:mb-4">
              <AgileIcon className="w-5 h-5 md:w-6 md:h-6 text-[#4DA1FF]" />
            </div>
            <h3 className="text-base md:text-lg font-semibold mb-2">Agile Excellence</h3>
            <p className="text-xs md:text-sm text-gray-300 max-w-xs leading-relaxed">
              We deliver with agility and technical excellence, ensuring rapid iterations, continuous improvement, and predictable outcomes.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OperatingPhilosophy;
